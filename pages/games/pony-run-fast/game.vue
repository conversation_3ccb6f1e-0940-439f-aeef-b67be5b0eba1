<template>
    <view class="game-container">
        <!-- 滚动背景容器 -->
        <view class="background-wrapper"
              :class="{ 'no-transition': isResettingBackground }"
              :style="{ transform: `translateY(${backgroundOffset}px)`}">
            <image v-for="index in 4" :key="index"
                   class="background-image" :src="images.bg" mode="widthFix"/>
        </view>
        
        <!-- 游戏元素层 -->
        <view class="game-layer" @click="onScreenClick">
            <view class="game-tips-msg-bar">
                <game-top-msg-bar right-icon="icon-rocket">
                    <template v-slot:leftMsg>{{ countdown }}</template>
                    <template v-slot:rightMsg>{{ score }}</template>
                </game-top-msg-bar>
            </view>
            
            <!-- 小马角色 -->
            <view class="pony-container">
                <image class="pony" :src="images.pony" mode="widthFix"/>
            </view>
            
            <!-- 鼓按钮 -->
            <view class="drum-container">
                <image class="drum" :src="images.drum" mode="aspectFit" @click.stop="onScreenClick"/>
            </view>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>

        <ready-countdown ref="readyCountdown" @countdownOver="readyCountdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'

const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/pony-run-fast/'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown},
    data() {
        return {
            gameState: 'ready', // 游戏状态：ready 准备阶段  //running 奔跑阶段  // gameover 游戏结束
            backgroundOffset: 0,         // 背景偏移量
            scrollStep: 100,              // 每次滚动的像素值
            images: {
                bg: `${BASE_URL}bg.png`,
                drum: `${BASE_URL}drum.png`,
                pony: `${BASE_URL}pony.png`
            },
            imgaeHeight: 0,
            isResettingBackground: false, // 控制背景重置时是否禁用transition动画

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                '游戏开始后，请快速连续点击屏幕。',
                '每点击1次屏幕向前奔跑1米。',
                `游戏倒计时${this.seconds}秒结束后结算，每奔跑1米奖励${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit
        this.per_integral = Number(params.per_integral)
        this.seconds = Number(params.seconds)
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.init()
    },

    onUnload() {
        this.clearCountdown()
    },

    methods: {
        async init() {
            await this.getGameData()
            await this.initBackgroundImages()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_img) this.images.bg = data.bg_img
            if (data.drum_img) this.images.drum = data.drum_img
            if (data.pony_img) this.images.pony = data.pony_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        async initBackgroundImages() {
            const {windowWidth} = uni.getWindowInfo()
            const {width, height} = await this.$uni.getImageInfo(this.images.bg)

            this.imgaeHeight = height * (windowWidth / width)

            this.$refs.gameTipsPopup.open()
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        readyCountdownOver() {
            this.gameState = 'running'
            this.startCountdown()
        },

        startCountdown() {
            this.countdown = this.seconds
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.gameOver()
                }
            }, 1000)
        },

        clearCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        // 处理屏幕点击事件
        onScreenClick() {
            if (this.gameState !== 'running') return

            // 增加分数
            this.score++

            // 执行背景滚动
            this.scrollBackground()
        },
        
        // 背景滚动动画
        scrollBackground() {
            const newOffset = this.backgroundOffset + this.scrollStep

            const maxOffset = this.imgaeHeight * 2

            if (newOffset < maxOffset) {
                // 更新背景偏移量
                this.backgroundOffset += this.scrollStep
                return
            }


            // 临时禁用transition动画，避免背景重置时的视觉跳跃
            this.isResettingBackground = true

            // 回到初始图片并到上次滚动的位置
            this.backgroundOffset = this.backgroundOffset - this.imgaeHeight

            // 在下一个DOM更新周期后重新启用transition动画
            // 确保背景位置重置完成后再恢复平滑动画效果
            setTimeout(() => {
                this.$nextTick(() => {
                    this.isResettingBackground = false

                    // 补回禁用动画那一次动画效果
                    this.backgroundOffset += this.scrollStep
                })
            }, 40)
        },

        gameOver() {
            if (this.gameState === 'gameover') return

            this.gameState = 'gameover'
            this.clearCountdown()
            this.submitResult()
        },

        async submitResult() {
            const count = this.score
            const sign = {
                types: 39,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },
    }
}
</script>

<style lang="scss" scoped>
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

/* 背景层 */
.background-wrapper {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
    transition: transform 0.3s ease;
}

/* 禁用transition动画的样式类，用于背景重置时避免视觉跳跃 */
.background-wrapper.no-transition {
    transition: none !important;
}

.background-image {
    position: relative;
    width: 100%;
    display: block;
}

/* 游戏元素层 */
.game-layer {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 10;
}

/* 小马角色 */
.pony-container {
    position: absolute;
    bottom: 400rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 120rpx;
}

.pony {
    width: 100%;
}

/* 鼓按钮 */
.drum-container {
    position: absolute;
    bottom: 80rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 240rpx;
    height: 240rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.drum {
    width: 100%;
    height: 100%;
}

/* 点击效果 - 使用动态类替代:active伪类 */
.drum:active {
    transform: scale(0.8);
    transition: transform 0.1s ease;
}

/* 鼓按钮的默认过渡效果 */
.drum {
    transition: transform 0.1s ease;
}
</style>