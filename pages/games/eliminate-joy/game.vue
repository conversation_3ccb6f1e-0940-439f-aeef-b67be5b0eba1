<template>
    <!-- 游戏主容器，设置背景图片和全屏布局 -->
    <view class="layout" :style="{'background-image': `url(${images.bgImg})`}">
        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ showScore }}</template>
            </game-top-msg-bar>
        </view>

        <view class="game-container">
            <view class="game-main" :style="{width: `${usableW}px`}">
                <view class="row" v-for="(row, index) in all_data" :key="index">
                    <view
                        class="star-item" v-for="(start, i) in row" :key="i"
                        :style="{width: `${cellW}px`, height: `${cellW}px`, transform: `translate(${start.translateX}px, ${start.translateY}px)`}"
                        :class="{
                            'deleted': start.is_deleted,
                            'is-selected': current_clicked_item && current_clicked_item.x == start.x && current_clicked_item.y == start.y,
                            'falling': start.isAnimating
                        }"
                        @touchstart="touchStart($event, start)" @touchend="touchEnd($event)"
                    >
                        <image class="start-image" :src="star_images[start.star_num]"/>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="scoreAnimationsShow" class="add-score-animation">+{{ addScore }}</view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>

        <ready-countdown ref="readyCountdown" @countdownOver="readyCountdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <view v-if="showAD" class="ad-container">
            <xwy-ad :ad_type="66"/>
        </view>
    </view>
</template>

<script>
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import gameTipsPopup from '../components/game-tips-popup.vue'
import readyCountdown from '../components/ready-countdown.vue'

const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/eliminating/'

const ALL_STAR_IMAGES = [
    `${BASE_URL}1.png`,
    `${BASE_URL}2.png`,
    `${BASE_URL}3.png`,
    `${BASE_URL}4.png`,
    `${BASE_URL}5.png`,
    `${BASE_URL}6.png`,
    `${BASE_URL}7.png`,
    `${BASE_URL}8.png`,
    `${BASE_URL}9.png`
]

export default {
    components: {readyCountdown, gameTipsPopup, gameTopMsgBar},
    // 组件数据定义
    data() {
        return {
            gameState: 'ready', // 游戏状态：ready 准备阶段  //running 游戏中  // gameover 游戏结束
            // 游戏得分
            score: 0,
            showScore: 0,
            // 游戏图片资源配置
            images: {
                bgImg: `${BASE_URL}bg.jpg`,
            },
            // 星星类型数据配置
            star_images: [],
            // 可用游戏区域宽度
            usableW: 0,
            // 单个格子的宽度
            cellW: 0,

            // 游戏主数据矩阵，存储所有星星的信息
            all_data: [],
            star_total: 6, // 图标数量 -- out --
            panel: { // x列 y行 -- out --
                x: 6,
                y: 8,
            },
            current_clicked_item: null, //当前已经选中的棋子 -- out --
            // 是否正在闪烁动画中（消除动画状态）
            is_blinking: false,
            // 是否正在下落动画中
            is_falling: false,
            executeEnd: false, // 是否执行touchEnd方法 -- out --

            // 触摸开始时的X坐标
            startX: 0,
            // 触摸开始时的Y坐标
            startY: 0,

            scoreAnimationsShow: false,
            addScore: 0,

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false,

            // 游戏结束状态管理
            gameEndRequested: false,    // 倒计时结束，等待游戏真正结束
            activeAnimations: 0,        // 当前活跃的动画数量
            eliminationInProgress: false, // 是否有消除操作正在进行
            maxWaitTime: 20000,         // 最大等待时间（20秒），防止死锁
            gameEndStartTime: null,     // 游戏结束开始时间
            gameForceStopped: false     // 游戏是否已强制停止（超时保护触发）
        };
    },

    computed: {
        tipsList() {
            return [
                '🎯 游戏目标：通过交换相邻元素，形成3个或以上相同元素的连线来消除得分。',
                '🎮 操作方式：点击选中元素后，再点击相邻元素进行交换，或直接滑动元素到相邻位置。',
                '💡 消除规则：相同元素需要横向或纵向连成一线（至少3个）才能消除，斜线不算。',
                `🏆 得分机制：倒计时${this.seconds}秒结束后，每消除1个元素获得${this.per_integral}${this.unit}。`
            ]
        }
    },

    watch: {
        score(val, oldVal) {
            if (val !== 0) {
                this.addScore = val - oldVal
                if (this.scoreAnimationsetTimeout) clearTimeout(this.scoreAnimationsetTimeout)
                this.$nextTick(() => {
                    this.scoreAnimationsShow = true
                    this.scoreAnimationsetTimeout = setTimeout(() => {
                        this.scoreAnimationsShow = false
                        this.showScore = val
                    }, 800)
                })
            }
        }
    },

    // 页面加载完成时的生命周期方法
    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit || '积分'
        this.per_integral = Number(params.per_integral) || 0
        this.seconds = Number(params.seconds) || 30
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)


        // 启动游戏初始化
        this.initGame()
    },

    onUnload() {
        this.clearCountdown()
        // 清理游戏结束状态，防止内存泄漏
        this.gameEndRequested = false
        this.activeAnimations = 0
        this.eliminationInProgress = false
        this.gameForceStopped = false
    },

    // 组件方法定义
    methods: {
        initGame() {
            // 计算游戏区域尺寸并初始化游戏数据
            this.getRect()
            this.init()

            this.$refs.gameTipsPopup.open()
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        readyCountdownOver() {
            this.gameState = 'running'
            this.startCountdown()
        },

        startCountdown() {
            this.countdown = this.seconds
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) {
                    this.gameOver()
                }
            }, 1000)
        },

        clearCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },

        /**
         * 计算游戏区域尺寸
         * 获取屏幕信息并计算可用宽度和单个格子宽度
         */
        getRect() {
            // 获取系统信息
            const {windowWidth, windowHeight} = uni.getWindowInfo()

            // 计算可用宽度（屏幕宽度减去左右边距40px）
            const usableW = windowWidth - 40
            // 计算可用宽度（屏幕宽度减去上下边距40px, 顶部倒计时得分栏55px, 广告高度150）
            const usableH = windowHeight - 40 - 55 - 150

            const {x, y} = this.panel

            // 计算单个格子的宽度，不能超出一屏
            let cellW = Math.floor(usableW / x)
            if (cellW * y > usableH) cellW = Math.floor(usableH / y)

            this.cellW = cellW
            this.usableW = usableW
        },



        /**
         * 初始化游戏
         * 初始化游戏数据并检查是否有可消除的方块
         */
        async init() {
            await this.getGameData()

            // 初始化可用星星图片列表
            this.getStarImages()

            // 初始化游戏矩阵数据
            this.initAllData()
            // this.checkPanelCanEliminating(); -- out --
        },


        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_img) this.images.bgImg = data.bg_img
            if (data.star_imgs?.length) this.all_star_images = data.star_imgs

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },


        // 初始化可用星星图片列表 随机从 all_star_images 获取 this.star_total个
        getStarImages() {
            const all_star_images = this.all_star_images || ALL_STAR_IMAGES

            // 如果 this.star_total 大于 all_star_images.length，this.star_total 改为 all_star_images.length
            if (this.star_total > all_star_images.length) this.star_total = all_star_images.length

            // 如果 this.star_total 等于 all_star_images.length，不需要随机，直接使用 all_star_images
            if (this.star_total === all_star_images.length) {
                this.star_images = all_star_images
                return
            }

            // 从 all_star_images 中随机获取 this.star_total 个图片
            this.star_images = this._utils.shuffleArray(all_star_images).slice(0, this.star_total)
        },


        /**
         * 初始化游戏矩阵数据
         * 创建一个二维数组，填充随机生成的星星元素
         */
        initAllData() {
            // 使用Array.from创建二维数组，直接填充星星元素
            this.all_data = Array.from({length: this.panel.y}, (_, j) =>
                Array.from({length: this.panel.x}, (_, i) => this.getDefaultItem(i, j))
            )
        },

        // 遍历每一个元素，如果可以消，则继续消 -- out --
        /**
         * 检查游戏面板是否有可消除的方块
         * 遍历所有元素，如果发现可消除的方块则执行消除操作
         */
        checkPanelCanEliminating() {
            // 如果游戏已被强制停止，立即返回，不执行任何消除逻辑
            if (this.isGameForceStopped()) {
                console.log('游戏已强制停止，跳过消除检查')
                return
            }

            // 使用some方法简化双重循环，找到第一个可消除的方块就停止
            const hasEliminable = this.all_data.some(row =>
                row.some(item => this.checkIfCanEliminating(item, this.all_data))
            )

            // 如果找到可消除的方块，执行消除操作
            if (hasEliminable) {
                this.checkCanEliminateAndClear()
            } else {
                // 没有可消除的方块，标记消除操作结束
                this.eliminationInProgress = false
                // 如果游戏结束已请求，检查是否可以真正结束游戏
                if (this.gameEndRequested) {
                    this.checkGameReallyEnd()
                }
            }
        },

        /**
         * 创建默认的星星元素对象
         * @param {number} x - 星星的X坐标（列）
         * @param {number} y - 星星的Y坐标（行）
         * @returns {Object} 星星元素对象
         */
        getDefaultItem(x, y) {
            return {
                x: x,                                                    // X坐标（列）
                y: y,                                                    // Y坐标（行）
                star_num: parseInt(Math.random() * this.star_total),     // 随机星星类型编号
                is_deleted: false,                                       // 是否已被删除标记
                translateX: 0,                                           // X轴位移动画值
                translateY: 0,                                           // Y轴位移动画值
                isAnimating: false,                                      // 是否正在下落动画中
                fallDistance: 0,                                         // 下落距离（用于动画计算）
            }
        },

        /**
         * 检查可消除的方块并清除
         * 设置闪烁动画，延时后清除矩阵并补充新方块，然后继续检查
         */
        checkCanEliminateAndClear() {
            // 如果游戏已被强制停止，立即返回，不执行消除逻辑
            if (this.isGameForceStopped()) {
                console.log('游戏已强制停止，跳过消除操作')
                return
            }

            // 清除当前选中的方块
            this.current_clicked_item = null
            // 标记消除操作正在进行
            this.eliminationInProgress = true
            // 开始消除动画
            this.startAnimation('elimination')
            // 开始闪烁动画
            this.is_blinking = true
            setTimeout(() => {
                // 在延时执行前再次检查游戏是否已被强制停止
                if (this.isGameForceStopped()) {
                    console.log('游戏已强制停止，取消延时消除操作')
                    this.is_blinking = false
                    this.endAnimation('elimination')
                    return
                }

                //查下是否有同样的点 -- out --
                // 清除矩阵中标记删除的方块并补充新方块（带下落动画）
                this.clearMatrixAndAddSupplementWithAnimation(this.all_data)
                // 结束闪烁动画
                this.is_blinking = false
                // 结束消除动画
                this.endAnimation('elimination')
            }, 500)
        },

        // 获取移动的方向 -- out --
        /**
         * 根据触摸起始和结束坐标判断移动方向
         * @param {number} x1 - 起始X坐标
         * @param {number} y1 - 起始Y坐标
         * @param {number} x2 - 结束X坐标
         * @param {number} y2 - 结束Y坐标
         * @returns {string|boolean} 移动方向('left'|'right'|'up'|'down')或false
         */
        getDirection(x1, y1, x2, y2) {
            // 获取x和y方向上的差值 正值则表示右或者下 -- out --
            const differX = x2 - x1, differY = y2 - y1

            // 计算X和Y方向的绝对值
            const absX = Math.abs(differX), absY = Math.abs(differY)

            // 超过方块的半径则表示移动了 -- out --
            // 如果移动距离小于格子宽度的一半，认为没有移动
            if (absX < this.cellW / 2 && absY < this.cellW / 2) return false

            // 判断x和y方向的绝对值的查 以便获取像哪个方向移动 -- out --
            // x轴差值大于y轴差值，则判断为左右移动 (differX > 0 ? 'right' : 'left') -- out --
            // y轴差值大于x轴差值，则判断为上下移动(differY > 0 ? 'down' : 'up') -- out --
            return absX > absY ? (differX > 0 ? 'right' : 'left') : (differY > 0 ? 'down' : 'up')
        },

        // 两个方块之间是否相邻（仅上下左右） -- out --
        /**
         * 判断两个方块是否相邻（仅限上下左右四个方向）
         * @param {Object} item1 - 第一个方块对象
         * @param {Object} item2 - 第二个方块对象
         * @returns {boolean} 是否相邻
         */
        judgeAdjoin(item1, item2) {
            // 判断是否相邻 -- out --
            // 获取两个方块的坐标
            const x1 = item1.x, y1 = item1.y, x2 = item2.x, y2 = item2.y
            // 曼哈顿距离为1表示相邻（仅上下左右）
            if (Math.abs(x1 - x2) + Math.abs(y1 - y2) == 1) return true
            // 如果不相邻，将第二个方块设为当前选中方块
            this.current_clicked_item = item2
            return false
        },

        /**
         * 触摸开始事件处理
         * @param {Event} e - 触摸事件对象
         * @param {Object} item - 被触摸的星星元素对象
         */
        touchStart(e, item) {
            // 如果正在闪烁动画或下落动画中或游戏不是进行中，不处理触摸事件
            // 如果游戏结束已请求或游戏已被强制停止，也不处理新的触摸事件
            if (this.is_blinking || this.is_falling || this.gameState !== 'running' ||
                this.gameEndRequested || this.isGameForceStopped()) return

            this.executeEnd = true

            // 处理已选中方块的情况
            if (this.current_clicked_item) {
                return this.handleSelectedItem(item)
            }

            // 处理游戏开始时的自动消除检查
            if (this.checkIfCanEliminating(item, this.all_data)) {
                return this.checkCanEliminateAndClear()
            }

            // 记录触摸坐标并设置当前选中方块
            this.recordTouchPosition(e)
            this.current_clicked_item = item
        },

        /**
         * 处理已选中方块的逻辑
         * @param {Object} item - 新点击的方块
         */
        handleSelectedItem(item) {
            this.executeEnd = false

            // 判断是否相邻
            const adjoin = this.judgeAdjoin(this.current_clicked_item, item)
            if (adjoin) {
                this.handleEliminate(item)
            }
            // 注意：judgeAdjoin 方法内部已经处理了不相邻时设置新选中方块的逻辑
        },

        /**
         * 记录触摸位置
         * @param {Event} e - 触摸事件对象
         */
        recordTouchPosition(e) {
            const {clientX, clientY} = e.changedTouches[0]
            this.startX = clientX
            this.startY = clientY
        },

        /**
         * 触摸结束事件处理
         * @param {Event} e - 触摸事件对象
         */
        touchEnd(e) {
            // 如果正在闪烁动画或下落动画中，或不允许执行touchEnd，直接返回
            if (this.is_blinking || this.is_falling || !this.executeEnd) return

            // 获取触摸结束坐标并计算移动方向
            const {clientX: x, clientY: y} = e.changedTouches[0]
            const direction = this.getDirection(this.startX, this.startY, x, y)
            if (!direction) return

            // 获取目标方块并处理消除逻辑
            const item = this.getNextItem(direction)
            if (!item) {
                this.current_clicked_item = null
                return
            }

            this.handleEliminate(item)
        },

        /**
         * 处理消除逻辑的核心方法
         * @param {Object} item - 目标方块对象
         */
        handleEliminate(item) {
            const currentItem = this.current_clicked_item

            // 如果两个方块是相同类型，只做位置交换动画，不进行消除
            if (item.star_num === currentItem.star_num) {
                this.changeTranslate(currentItem, item)
                this.current_clicked_item = null
                return
            }

            // 创建交换后的新矩阵并检查是否可以消除
            const newMatrix = this.getNewMatrix(currentItem, item, this.all_data)
            const canEliminateTarget = this.checkIfCanEliminating(newMatrix[item.y][item.x], newMatrix)
            const canEliminateSource = this.checkIfCanEliminating(newMatrix[currentItem.y][currentItem.x], newMatrix)

            // 如果交换后两个位置都不能消除，则只做交换动画后恢复
            if (!canEliminateTarget && !canEliminateSource) {
                this.changeTranslate(currentItem, item)
                this.current_clicked_item = null
                return
            }

            // 执行有效交换：先播放动画，再更新数据并检查消除
            this.changeTranslate(currentItem, item, true)
            setTimeout(() => {
                this.all_data = newMatrix
                this.checkCanEliminateAndClear()
            }, 150)
        },


        /**
         * 根据移动方向获取下一个方块
         * @param {string} direction - 移动方向('left'|'right'|'up'|'down')
         * @returns {Object|boolean} 目标方块对象或false（如果超出边界）
         */
        getNextItem(direction) {
            const {x, y} = this.current_clicked_item

            // 根据移动方向计算目标坐标
            const directions = {
                left: [x - 1, y],
                right: [x + 1, y],
                up: [x, y - 1],
                down: [x, y + 1]
            }

            const [targetX, targetY] = directions[direction]

            // 检查目标坐标是否超出游戏面板边界
            if (targetX < 0 || targetY < 0 || targetX >= this.panel.x || targetY >= this.panel.y) {
                return false
            }

            // 返回目标位置的方块对象
            return this.all_data[targetY][targetX]
        },


        /**
         * 改变方块的位移动画
         * @param {Object} item1 - 第一个方块对象
         * @param {Object} item2 - 第二个方块对象
         * @param {boolean} stop - 是否停止动画（不恢复原位）
         */
        changeTranslate(item1, item2, stop) {
            // 计算位移距离
            const deltaX1 = (item2.x - item1.x) * this.cellW
            const deltaY1 = (item2.y - item1.y) * this.cellW
            const deltaX2 = -deltaX1
            const deltaY2 = -deltaY1

            // 设置两个方块的位移
            this.setItemTranslate(item1, deltaX1, deltaY1)
            this.setItemTranslate(item2, deltaX2, deltaY2)

            // 如果不需要恢复原位，直接返回
            if (stop) return

            // 150ms后恢复原位（用于无效交换的动画反馈）
            setTimeout(() => {
                this.setItemTranslate(item1, 0, 0)
                this.setItemTranslate(item2, 0, 0)
            }, 150)
        },

        /**
         * 设置方块的位移并更新到矩阵中
         * @param {Object} item - 方块对象
         * @param {number} translateX - X轴位移
         * @param {number} translateY - Y轴位移
         */
        setItemTranslate(item, translateX, translateY) {
            item.translateX = translateX
            item.translateY = translateY
            this.$set(this.all_data[item.y], item.x, item)
        },


        /**
         * 清理游戏矩阵中标记为删除的方块，并从上方补充新方块（带下落动画）
         * @param {Array} all_matrix - 游戏矩阵数据
         * @returns {Promise} 动画完成的Promise
         */
        async clearMatrixAndAddSupplementWithAnimation(all_matrix) {
            // 如果游戏已被强制停止，立即返回，不执行补位逻辑
            if (this.isGameForceStopped()) {
                console.log('游戏已强制停止，跳过元素补位操作')
                return
            }

            // 开始下落动画
            this.startAnimation('falling')
            // 开始下落动画状态
            this.is_falling = true

            // 深拷贝矩阵数据
            const temp_matrix = JSON.parse(JSON.stringify(all_matrix))
            let count = 0  // 记录消除的方块数量用于计分

            // 遍历每一列进行清理和补充计算
            for (let x = 0; x < this.panel.x; x++) {
                // 收集当前列中未被删除的方块（从下往上）
                const remainingBlocks = []
                for (let y = this.panel.y - 1; y >= 0; y--) {
                    if (!temp_matrix[y][x].is_deleted) {
                        remainingBlocks.push(temp_matrix[y][x])
                    }
                }

                // 计算需要补充的方块数量
                const needNewBlocks = this.panel.y - remainingBlocks.length
                count += needNewBlocks

                // 重新填充当前列：先放置保留的方块，再补充新方块
                for (let y = 0; y < this.panel.y; y++) {
                    const blockIndex = this.panel.y - 1 - y

                    if (blockIndex < remainingBlocks.length) {
                        // 使用保留的方块，更新其坐标，并计算下落距离
                        const originalBlock = remainingBlocks[blockIndex]
                        const fallDistance = (y - originalBlock.y) * this.cellW

                        temp_matrix[y][x] = Object.assign({}, originalBlock, {
                            x,
                            y,
                            fallDistance: fallDistance,
                            isAnimating: fallDistance > 0,
                            translateY: fallDistance > 0 ? -fallDistance : 0  // 初始位置在上方
                        })
                    } else {
                        // 生成新的随机方块，设置初始位置在屏幕上方
                        // 新方块从更高的位置开始下落，确保动画明显
                        const newBlockIndex = blockIndex - remainingBlocks.length
                        const extraHeight = (newBlockIndex + 1) * this.cellW  // 额外的高度，让动画更明显
                        const newBlockFallDistance = extraHeight + this.cellW  // 至少下落一个格子的距离

                        temp_matrix[y][x] = Object.assign(this.getDefaultItem(x, y), {
                            isAnimating: true,
                            fallDistance: newBlockFallDistance,
                            translateY: -newBlockFallDistance  // 初始位置在上方
                        })
                    }
                }
            }

            // 根据消除的方块数量增加得分
            this.score += count

            // 更新游戏数据（此时方块在初始位置）
            this.$set(this, 'all_data', temp_matrix)

            // 等待DOM更新后开始下落动画
            await this.$nextTick()

            // 在开始动画前再次检查游戏是否已被强制停止
            if (this.isGameForceStopped()) {
                console.log('游戏已强制停止，取消下落动画')
                this.is_falling = false
                this.endAnimation('falling')
                return
            }

            // 添加一个短暂延迟，确保初始位置渲染完成
            await new Promise(resolve => setTimeout(resolve, 50))

            // 再次检查强制停止状态
            if (this.isGameForceStopped()) {
                console.log('游戏已强制停止，取消下落动画')
                this.is_falling = false
                this.endAnimation('falling')
                return
            }

            // 开始下落动画
            await this.startFallAnimation()

            // 动画完成，重置状态
            this.resetAnimationState()
            this.is_falling = false
            // 结束下落动画
            this.endAnimation('falling')

            // 继续检查是否有可消除的方块（仅在游戏未强制停止时）
            if (!this.isGameForceStopped()) {
                this.$nextTick(() => this.checkPanelCanEliminating())
            }
        },

        /**
         * 开始下落动画
         * @returns {Promise} 动画完成的Promise
         */
        async startFallAnimation() {
            return new Promise((resolve) => {
                // 如果游戏已被强制停止，立即完成动画
                if (this.isGameForceStopped()) {
                    console.log('游戏已强制停止，立即完成下落动画')
                    resolve()
                    return
                }

                // 触发下落动画：将所有需要动画的方块的translateY设置为0
                this.all_data.forEach(row => {
                    row.forEach(item => {
                        if (item.isAnimating) {
                            // 使用 $set 确保响应式更新
                            this.$set(item, 'translateY', 0)
                        }
                    })
                })

                // 增加动画持续时间，让效果更明显（从400ms增加到800ms）
                // 但在动画过程中定期检查强制停止状态
                let animationTime = 0
                const checkInterval = 100 // 每100ms检查一次
                const totalTime = 800

                const checkTimer = setInterval(() => {
                    animationTime += checkInterval

                    // 如果游戏被强制停止，立即结束动画
                    if (this.isGameForceStopped()) {
                        clearInterval(checkTimer)
                        console.log('动画过程中检测到游戏强制停止，立即结束动画')
                        resolve()
                        return
                    }

                    // 如果动画时间到达，正常结束
                    if (animationTime >= totalTime) {
                        clearInterval(checkTimer)
                        resolve()
                    }
                }, checkInterval)
            })
        },

        /**
         * 重置所有方块的动画状态
         */
        resetAnimationState() {
            this.all_data.forEach(row => {
                row.forEach(item => {
                    if (item.isAnimating) {
                        this.$set(item, 'isAnimating', false)
                        this.$set(item, 'fallDistance', 0)
                        // 确保translateY也被重置
                        this.$set(item, 'translateY', 0)
                    }
                })
            })
        },

        /**
         * 保留原有的清除方法（用于不需要动画的场景）
         * @param {Array} all_matrix - 游戏矩阵数据
         * @returns {Array} 清理并补充后的新矩阵
         */
        clearMatrixAndAddSupplement(all_matrix) {
            // 深拷贝矩阵数据
            const temp_matrix = JSON.parse(JSON.stringify(all_matrix))
            let count = 0  // 记录消除的方块数量用于计分

            // 遍历每一列进行清理和补充
            for (let x = 0; x < this.panel.x; x++) {
                // 收集当前列中未被删除的方块（从下往上）
                const remainingBlocks = []
                for (let y = this.panel.y - 1; y >= 0; y--) {
                    if (!temp_matrix[y][x].is_deleted) {
                        remainingBlocks.push(temp_matrix[y][x])
                    }
                }

                // 计算需要补充的方块数量
                const needNewBlocks = this.panel.y - remainingBlocks.length
                count += needNewBlocks

                // 重新填充当前列：先放置保留的方块，再补充新方块
                for (let y = 0; y < this.panel.y; y++) {
                    const blockIndex = this.panel.y - 1 - y

                    if (blockIndex < remainingBlocks.length) {
                        // 使用保留的方块，更新其坐标
                        temp_matrix[y][x] = Object.assign({}, remainingBlocks[blockIndex], {x, y})
                    } else {
                        // 生成新的随机方块
                        temp_matrix[y][x] = this.getDefaultItem(x, y)
                    }
                }
            }

            // 根据消除的方块数量增加得分
            this.score += count
            // 更新游戏数据
            this.$set(this, 'all_data', temp_matrix)
            return temp_matrix
        },


        /**
         * 创建交换两个方块后的新矩阵
         * @param {Object} from_obj - 起始方块对象
         * @param {Object} to_obj - 目标方块对象
         * @param {Array} all_matrix - 当前游戏矩阵
         * @returns {Array} 交换后的新矩阵
         */
        getNewMatrix(from_obj, to_obj, all_matrix) {
            // 深拷贝当前矩阵
            const new_matrix = JSON.parse(JSON.stringify(all_matrix))

            // 直接交换两个位置的方块并更新坐标
            new_matrix[to_obj.y][to_obj.x] = {...from_obj, x: to_obj.x, y: to_obj.y}
            new_matrix[from_obj.y][from_obj.x] = {...to_obj, x: from_obj.x, y: from_obj.y}

            return new_matrix
        },

        /**
         * 获取指定坐标的方块，如果坐标超出边界则返回null
         * @param {number} y - Y坐标（行）
         * @param {number} x - X坐标（列）
         * @param {Array} all_matrix - 游戏矩阵
         * @returns {Object|null} 方块对象或null
         */
        getItemOrNull(y, x, all_matrix) {
            // 检查坐标是否在游戏面板范围内
            if (x >= 0 && x < this.panel.x && y >= 0 && y < this.panel.y) {
                return all_matrix[y][x]
            }
            return null
        },


        /**
         * 获取与指定方块相邻的相同类型方块数组
         * @param {Object} now_item - 当前方块对象
         * @param {Array} all_matrix - 游戏矩阵
         * @param {boolean} is_getting_extra - 是否获取额外的连锁方块
         * @returns {Array|boolean} 相同类型的相邻方块数组，如果不满足消除条件则返回false
         */
        getNextToSameArr(now_item, all_matrix, is_getting_extra = false) {
            const {x, y} = now_item
            const sameBlocks = []

            // 四个方向搜索相同类型方块
            const directions = [
                {dx: -1, dy: 0}, // 左
                {dx: 1, dy: 0},  // 右
                {dx: 0, dy: -1}, // 上
                {dx: 0, dy: 1}   // 下
            ]

            directions.forEach(({dx, dy}) => {
                let currentX = x + dx
                let currentY = y + dy

                // 沿着当前方向搜索
                while (this.isValidPosition(currentX, currentY)) {
                    const item = all_matrix[currentY][currentX]

                    if (item.star_num === now_item.star_num && !item.is_deleted) {
                        sameBlocks.push(item)
                        currentX += dx
                        currentY += dy
                    } else {
                        break
                    }
                }
            })

            // 递归调用处理连锁消除
            if (is_getting_extra) {
                if (sameBlocks.length > 0) {
                    this.markBlocksAsDeleted(sameBlocks, all_matrix)
                    const chainBlocks = this.getChainBlocks(sameBlocks, all_matrix)
                    return [...chainBlocks, ...sameBlocks]
                }
                return sameBlocks
            }

            // 检查消除条件
            if (!this.isValidForElimination(sameBlocks)) {
                return false
            }

            // 标记删除并处理连锁
            this.markBlocksAsDeleted([...sameBlocks, now_item], all_matrix)
            const chainBlocks = this.getChainBlocks(sameBlocks, all_matrix)

            return [...sameBlocks, ...chainBlocks, now_item]
        },


        /**
         * 检查指定方块是否可以消除
         * @param {Object} now_item - 要检查的方块对象
         * @param {Array} all_matrix - 游戏矩阵
         * @returns {Array|boolean} 可消除的方块数组或false
         */
        checkIfCanEliminating(now_item, all_matrix) {
            return this.getNextToSameArr(now_item, all_matrix)
        },


        /**
         * 检查坐标是否有效
         */
        isValidPosition(x, y) {
            return x >= 0 && x < this.panel.x && y >= 0 && y < this.panel.y
        },


        /**
         * 标记方块为删除状态
         */
        markBlocksAsDeleted(blocks, all_matrix) {
            blocks.forEach(block => {
                all_matrix[block.y][block.x].is_deleted = true
            })
        },


        /**
         * 获取连锁消除的方块
         */
        getChainBlocks(blocks, all_matrix) {
            const chainBlocks = []
            blocks.forEach(block => {
                const newBlocks = this.getNextToSameArr(block, all_matrix, true)
                chainBlocks.push(...newBlocks)
            })
            return chainBlocks
        },


        /**
         * 检查是否满足消除条件
         */
        isValidForElimination(blocks) {
            if (blocks.length < 2) return false

            if (blocks.length === 2) {
                // 两个方块必须在同一行或同一列
                return blocks[0].x === blocks[1].x || blocks[0].y === blocks[1].y
            }

            return true
        },


        gameOver() {
            if (this.gameState === 'gameover') return

            this.gameState = 'gameover'
            this.clearCountdown()

            // 设置游戏结束请求标志和开始时间
            this.gameEndRequested = true
            this.gameEndStartTime = Date.now()

            // 立即检查是否可以结束游戏（如果没有正在进行的动画）
            this.checkGameReallyEnd()
        },

        /**
         * 开始动画计数
         * @param {string} type - 动画类型（'elimination' | 'falling'）
         */
        startAnimation(type) {
            this.activeAnimations++
            console.log(`开始${type}动画，当前活跃动画数量: ${this.activeAnimations}`)
        },

        /**
         * 结束动画计数
         * @param {string} type - 动画类型（'elimination' | 'falling'）
         */
        endAnimation(type) {
            this.activeAnimations = Math.max(0, this.activeAnimations - 1)
            console.log(`结束${type}动画，当前活跃动画数量: ${this.activeAnimations}`)

            // 如果游戏结束已请求，检查是否可以真正结束游戏
            if (this.gameEndRequested) {
                this.checkGameReallyEnd()
            }
        },

        /**
         * 检查游戏是否真正结束，可以提交分数
         * 只有当所有动画完成且没有消除操作正在进行时，才提交分数
         */
        checkGameReallyEnd() {
            // 如果游戏结束未请求，直接返回
            if (!this.gameEndRequested) return

            // 检查是否超过最大等待时间
            const currentTime = Date.now()
            const waitTime = currentTime - this.gameEndStartTime
            if (waitTime > this.maxWaitTime) {
                console.warn('等待游戏结束超时，强制停止所有游戏逻辑并提交分数')
                this.forceStopGame()
                return
            }

            // 检查是否还有活跃的动画或消除操作正在进行
            if (this.activeAnimations > 0 || this.eliminationInProgress) {
                console.log(`游戏尚未真正结束 - 活跃动画: ${this.activeAnimations}, 消除进行中: ${this.eliminationInProgress}`)
                // 延迟100ms后再次检查
                setTimeout(() => this.checkGameReallyEnd(), 100)
                return
            }

            // 所有动画和操作都已完成，可以提交分数
            console.log('游戏真正结束，提交最终分数:', this.score)
            this.submitResult()
        },

        /**
         * 强制停止游戏的所有逻辑操作（超时保护机制）
         * 立即停止所有动画、消除操作和用户交互
         */
        forceStopGame() {
            console.warn('触发强制停止游戏机制')

            // 设置强制停止标志
            this.gameForceStopped = true

            // 立即停止所有动画状态
            this.is_blinking = false
            this.is_falling = false
            this.activeAnimations = 0
            this.eliminationInProgress = false

            // 清除当前选中的方块，阻止新的交互
            this.current_clicked_item = null

            // 停止所有正在进行的动画效果
            this.forceStopAllAnimations()

            // 立即提交分数
            console.warn('强制停止完成，提交最终分数:', this.score)
            this.submitResult()
        },

        /**
         * 强制停止所有动画效果
         * 重置所有方块的动画状态和位移
         */
        forceStopAllAnimations() {
            if (!this.all_data || !Array.isArray(this.all_data)) return

            this.all_data.forEach(row => {
                if (!Array.isArray(row)) return
                row.forEach(item => {
                    if (item && typeof item === 'object') {
                        // 重置所有动画相关属性
                        this.$set(item, 'isAnimating', false)
                        this.$set(item, 'fallDistance', 0)
                        this.$set(item, 'translateX', 0)
                        this.$set(item, 'translateY', 0)
                        // 确保删除标记被清除（避免显示异常）
                        this.$set(item, 'is_deleted', false)
                    }
                })
            })

            console.log('所有动画效果已强制停止')
        },

        /**
         * 检查游戏是否已被强制停止
         * @returns {boolean} 游戏是否已强制停止
         */
        isGameForceStopped() {
            return this.gameForceStopped || (this.gameEndRequested &&
                   this.gameEndStartTime &&
                   (Date.now() - this.gameEndStartTime) > this.maxWaitTime)
        },

        async submitResult() {
            if (this.isSubmit) return

            this.isSubmit = true

            const count = this.score
            const sign = {
                types: 40,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            uni.hideLoading()

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>


<style lang="scss" scoped>
.layout {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-size: cover;
    background-repeat: no-repeat;
}

.game-container {
    padding: 20px;

    .game-main {
        .row {
            display: flex;
            flex-direction: row;
            justify-content: center;

            .star-item {
                transition: transform .15s ease;

                /* 下落动画时使用更长的过渡时间和自然的缓动效果 */
                &.falling {
                    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                    /* 添加阴影效果，增强视觉冲击力 */
                    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
                    /* 添加轻微的缩放效果 */
                    transform-origin: center center;
                }

                /* 为下落动画添加更明显的视觉效果 */
                &.falling .start-image {
                    animation: fallBounce 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                }

                .start-image {
                    width: 100%;
                    height: auto;
                    aspect-ratio: 1;
                    display: block;
                }
            }
        }

        .is-selected {
            border-radius: 4px;
            background: repeating-conic-gradient(from var(--range), #0ff, #f0f, #0ff, #ff0, #0f0);
            animation: rotating 2s linear infinite;
        }

        /* 被删除方块的闪烁动画样式 */
        .deleted {
            animation: blink 0.2s linear infinite; /* 无限循环的闪烁动画 */
        }
    }
}

@property --range {
    initial-value: 0deg;
    syntax: '<angle>';
    inherits: false;
}

@keyframes rotating {
    0% {
        --range: 0deg;
    }
    100% {
        --range: 360deg;
    }
}

/* 闪烁动画关键帧定义 */
@keyframes blink {
    0% {
        opacity: 1; /* 开始时完全不透明 */
    }

    100% {
        opacity: 0; /* 结束时完全透明 */
    }
}

/* 下落动画关键帧定义 - 添加轻微的弹跳和缩放效果 */
@keyframes fallBounce {
    0% {
        transform: scale(1.1);
        opacity: 0.8;
    }

    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}


.add-score-animation {
    position: fixed;
    top: 30vh;
    left: 50vw;
    transform: translateX(-50%) scale(2);
    pointer-events: none;
    z-index: 1000;
    font-size: 100rpx;
    font-weight: bold;
    color: #ffffff;
    text-shadow:
        2px 2px 0 #000000,
        -2px -2px 0 #000000,
        2px -2px 0 #000000,
        -2px 2px 0 #000000,
        0 4px 8px rgba(0, 0, 0, 0.5); /* 黑色描边和阴影 */

    /* 添加发光效果 */
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
    letter-spacing: 5px;

    animation: score-animation .8s linear forwards;
}

@keyframes score-animation {
    0% {
        transform: translateX(-50%) scale(0);
        opacity: 0;
        top: 30vh;
        left: 50vw;
    }
    30% {
        transform: translateX(-50%) scale(2);
        opacity: 1;
        top: 30vh;
        left: 50vw;
    }
    60% {
        transform: translateX(-50%) scale(2);
        opacity: 1;
        top: 30vh;
        left: 50vw;
    }
    80% {
        transform: translateX(-50%) scale(.3);
        opacity: 1;
        top: 20px;
        left: calc(100vw - 60px);
    }
    100% {
        transform: translateX(-50%) scale(.3);
        opacity: 0;
        top: 20px;
        left: calc(100vw - 60px);
    }
}
</style>