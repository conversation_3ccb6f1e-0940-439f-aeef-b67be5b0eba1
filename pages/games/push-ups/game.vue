
<template>
    <view class="game-content" :style="{ 'background-image': `url(${images.bgImg})` }">

        <!-- 弹幕组件 -->
        <biubiubiu v-if="gameStatus" ref="danmakuComponent" />

        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ score }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup
            ref="gameTipsPopup"
            :tips-list="tipsList"
            :show-ad="showAD"
            @startGame="gameTipsClose"
        />

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <!-- 游戏区域 -->
        <view class="game-area">
            <!-- 俯卧撑运动 -->
            <view class="exercise-inflator">
                <image
                    :style="{ opacity: !pushUpsStatus ? 1 : 0 }"
                    :src="images.peopleUpImg"
                    mode="widthFix"
                />
                <image
                    :style="{ opacity: pushUpsStatus ? 1 : 0 }"
                    :src="images.peopleDownImg"
                    mode="widthFix"
                />
            </view>
        </view>

        <view v-if="gameStatus === 1" class="button-container">
            <view
                class="push-button"
                :class="{ active: isButtonClicked }"
                @click.stop="pushUpsClick"
            >
                <image :src="images.pushBtnImg" mode="widthFix"/>
            </view>
        </view>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'
import biubiubiu from './biubiubiu.vue'

const baseUrl = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/sport/'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown, biubiubiu},
    data() {
        return {
            images: {
                bgImg: `${baseUrl}bg.jpg`, // 背景图片
                peopleUpImg: `${baseUrl}p1.jpg`, // 人物 上
                peopleDownImg: `${baseUrl}p2.jpg`, // 人物 下
                pushBtnImg: `${baseUrl}button.png`, // 俯卧撑按钮图片
            },
            defaultScore: 1, // 默认每次得1分

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            score: 0,
            pushUpsStatus: false,
            isButtonClicked: false, // 按钮点击状态

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                `请在${this.seconds}秒内点击屏幕中的按钮，每次点击完成一个俯卧撑。`,
                `倒计时结束后，游戏结束，每完成一个俯卧撑奖励${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit
        this.per_integral = Number(params.per_integral)
        this.seconds = Number(params.seconds)
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.init()
    },

    methods: {
        async init() {
            await this.getGameData()
            this.$refs.gameTipsPopup.open()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData()

            if (data.bg_img) this.images.bgImg = data.bg_img
            if (data.people_up_img) this.images.peopleUpImg = data.people_up_img
            if (data.people_down_img) this.images.peopleDownImg = data.people_down_img
            if (data.push_btn_img) this.images.pushBtnImg = data.push_btn_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => this.getOpenerEventChannel().once('data', data => resolve(data)))
        },

        gameTipsClose() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startGame()
        },

        // 开始游戏
        startGame() {
            this.gameStatus = 1
            this.countdown = this.seconds
            this.score = 0
            this.pushUpsStatus = false
            // 清空弹幕组件中的弹幕
            if (this.$refs.danmakuComponent) {
                this.$refs.danmakuComponent.clearDanmaku()
            }
            this.startTimer()
        },

        // 开始计时
        startTimer() {
            this.timer = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.endGame()
            }, 1000)
        },


        // 结束游戏
        endGame() {
            this.gameStatus = 2
            if (this.timer) {
                clearInterval(this.timer)
                this.timer = null
            }

            this.submitResult()
        },

        // 运动点击
        pushUpsClick() {
            if (this.gameStatus !== 1) return

            // 触发点击动画
            this.isButtonClicked = true

            // 调用弹幕组件添加弹幕
            if (this.$refs.danmakuComponent) {
                this.$refs.danmakuComponent.addDanmaku()
            }

            setTimeout(() => (this.isButtonClicked = false), 200)

            this.pushUpsStatus = !this.pushUpsStatus
            this.score += this.defaultScore
        },


        async submitResult() {
            const count = this.score
            const sign = {
                types: 48,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? `完成${count}个俯卧撑` : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    },

    onUnload() {
        if (this.timer) clearInterval(this.timer)
    },
}
</script>

<style scoped lang="scss">
/* ===== 主要组件样式 ===== */
// 游戏主容器
.game-content {
    /* 布局属性 */
    position: relative;
    width: 100%;
    min-height: 100vh;

    /* 背景属性 */
    background-size: cover;
    background-repeat: no-repeat;
}

// 游戏区域
.game-area {
    /* 布局属性 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding-top: 72%;
}

// 运动器械容器
.exercise-inflator {
    /* 定位属性 */
    position: relative;

    /* 布局属性 */
    display: flex;

    /* 尺寸属性 */
    width: 80vw;
    max-width: 512px;
    height: 60vw;
    max-height: 384px;

    // 器械图片
    > image {
        /* 定位属性 */
        position: absolute;
        top: 0;
        left: 0;
        display: block;

        /* 尺寸属性 */
        width: 80vw;
        max-width: 512px;
        height: 60vw;
        max-height: 384px;
    }
}

// 按钮容器
.button-container {
    /* 布局属性 */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    /* 间距属性 */
    padding-top: 2rem;
}

// 运动按钮
.push-button {
    /* 定位属性 */
    position: relative;

    /* 布局属性 */
    display: flex;
    width: 50%;

    /* 动画效果 */
    transition: all 0.05s;

    &:active {
        transform: scale(0.9);
    }

    > image {
        width: 100%;
        height: auto;
        display: block;
    }
}
</style>
