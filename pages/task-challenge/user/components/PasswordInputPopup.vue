<template>
    <!-- 密码输入弹窗组件 -->
    <uni-popup ref="passwordPopup" :is-mask-click="false" @maskClick="handleMaskClick">
        <view class="password-popup">
            <view class="password-popup-title">活动密码</view>
            <view class="popup-container">
                <view class="password-popup-input">
                    <uni-easyinput 
                        v-model="inputPassword" 
                        placeholder="请输入活动密码"
                        @input="handlePasswordInput"
                    />
                </view>
                <view class="password-popup-buttons">
                    <view class="password-popup-cancel-button" @click="handleCancel">取消</view>
                    <view class="password-popup-confirm-button" @click="handleConfirm">确定</view>
                </view>
            </view>
        </view>
    </uni-popup>
</template>

<script>
export default {
    name: 'PasswordInputPopup',
    props: {
        // 活动ID
        activeId: {
            type: String,
            required: true
        },
        // 是否显示纯净版相关功能
        shieldOther: {
            type: <PERSON>olean,
            default: false
        }
    },
    data() {
        return {
            inputPassword: '' // 输入的密码
        }
    },
    methods: {
        /**
         * 打开密码输入弹窗
         * @param {String} defaultPassword 默认密码值
         */
        open(defaultPassword = '') {
            this.inputPassword = defaultPassword
            this.$refs.passwordPopup.open()
        },

        /**
         * 关闭密码输入弹窗
         */
        close() {
            this.$refs.passwordPopup.close()
        },

        /**
         * 处理密码输入
         * @param {String} value 输入的密码值
         */
        handlePasswordInput(value) {
            this.inputPassword = value
        },

        /**
         * 处理遮罩点击事件
         */
        handleMaskClick() {
            this.$emit('maskClick', this.activeId)
        },

        /**
         * 处理确认按钮点击
         */
        handleConfirm() {
            const password = this.inputPassword.trim()
            
            if (!password) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.shieldOther) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res.cancel) {
                                uni.navigateTo({
                                    url: '/pages/user/user'
                                })
                            }
                        }
                    })
                }
                
                return this.$uni.showToast('请输入密码')
            }
            
            // 触发密码确认事件
            this.$emit('confirm', password)
        },

        /**
         * 处理取消按钮点击
         */
        handleCancel() {
            this.$emit('cancel')
        }
    }
}
</script>

<style lang="scss" scoped>
.password-popup {
    width: 80vw;
    padding: 0 30px;
    box-sizing: border-box;
    position: relative;
    background-color: #FFD67A;
    border-radius: 10px;
    padding: 10px;

    .popup-container {
        background-color: #fff;
        border-radius: 10px;
        padding: 10px;
    }

    .password-popup-title {
        position: absolute;
        top: -22px;
        left: 50%;
        margin-left: -110px;
        width: 220px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url("https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .password-popup-input {
        padding: 10px 0;
    }

    .password-popup-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 0 10px 0;

        .password-popup-confirm-button,
        .password-popup-cancel-button {
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            margin: 10px;
            font-size: 18px;
        }

        .password-popup-confirm-button {
            background-color: #ff985e;
            color: #fff;
        }

        .password-popup-cancel-button {
            background-color: #f8dda0;
            color: #f1f1f1;
        }
    }
}
</style>
