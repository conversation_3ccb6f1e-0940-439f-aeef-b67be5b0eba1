<template>
    <!-- 顶部状态栏组件 -->
    <template v-if="isJoining">
        <!-- 左侧关卡信息 -->
        <view v-if="!justOneTask" class="top-msg top-msg-left" @click="handleGoToLevel">
            <view class="top-msg-icon">
                <view class="top-msg-inner-ring">
                    <text class="iconfont icon-map"></text>
                </view>
            </view>
            <view class="top-msg-num">
                <template v-if="justOneLevelNotLevel">
                    <template v-if="isNaN(activeDays)">{{ activeDays }}</template>
                    <template v-else>第{{ activeDays }}天</template>
                </template>
                <template v-else>{{ currentLevelName }}</template>
            </view>
        </view>

        <!-- 右侧积分信息 -->
        <view class="top-msg top-msg-right" @click="handleLookIntegralRecord">
            <view class="top-msg-icon">
                <view class="top-msg-inner-ring">
                    <text class="iconfont icon-integral"></text>
                </view>
            </view>
            <view class="top-msg-num" :class="{'need-show-text' : !!rankSet.medalSetting}">
                <text v-if="rankSet.medalSetting" class="font12 pr5">我的{{ integralUnit }}:</text>
                <template>
                    <template v-if="rankSet.rushing_round_index_current_day_integral">
                        {{ todayIntegral }}
                    </template>
                    <template v-else>{{ userDetails.integral_all || 0 }}</template>
                </template>
            </view>
        </view>

        <!-- 勋章信息 -->
        <view 
            v-if="rankSet.medalSetting" 
            class="top-msg top-msg-right top-msg-medal"
            @click="handleLookMedal"
        >
            <view class="top-msg-icon">
                <view class="top-msg-inner-ring">
                    <text class="iconfont icon-medal"></text>
                </view>
            </view>
            <view class="top-msg-num need-show-text">
                <text class="font12 pr5">我的勋章:</text>
                <text>{{ userDetails.medal_num || 0 }}</text>
            </view>
        </view>
    </template>
</template>

<script>
export default {
    name: 'TopStatusBar',
    props: {
        // 是否已参与活动
        isJoining: {
            type: Boolean,
            default: false
        },
        // 是否只有一个任务
        justOneTask: {
            type: Boolean,
            default: false
        },
        // 是否只有一关且不显示关卡
        justOneLevelNotLevel: {
            type: Boolean,
            default: false
        },
        // 活动天数
        activeDays: {
            type: [String, Number],
            default: 0
        },
        // 当前关卡名称
        currentLevelName: {
            type: String,
            default: ''
        },
        // 积分单位
        integralUnit: {
            type: String,
            default: '积分'
        },
        // 排行设置
        rankSet: {
            type: Object,
            default: () => ({})
        },
        // 今日积分
        todayIntegral: {
            type: Number,
            default: 0
        },
        // 用户详情
        userDetails: {
            type: Object,
            default: () => ({})
        }
    },
    methods: {
        /**
         * 处理前往关卡
         */
        handleGoToLevel() {
            this.$emit('goToLevel')
        },

        /**
         * 处理查看积分记录
         */
        handleLookIntegralRecord() {
            this.$emit('lookIntegralRecord')
        },

        /**
         * 处理查看勋章
         */
        handleLookMedal() {
            this.$emit('lookMedal')
        }
    }
}
</script>

<style lang="scss" scoped>
.top-msg {
    position: fixed;
    top: 10px;
    display: flex;
    flex-direction: row;

    .top-msg-icon {
        position: absolute;
        left: -15px;
        top: -3px;
        border-radius: 50%;
        padding: 3px;
        
        .top-msg-inner-ring {
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
            
            .iconfont {
                color: #fff;
            }
        }
    }

    .top-msg-num {
        background-color: rgba(0, 0, 0, .3);
        color: #fff;
        height: 24px;
        line-height: 24px;
        border-radius: 0 12px 12px 0;
        padding: 0 12px 0 20px;
        min-width: 60px;
        text-align: center;
        font-size: 14px;

        &.need-show-text {
            min-width: 100px;
        }
    }
}

.top-msg-left {
    left: calc(3vw + 15px);

    .top-msg-icon {
        background-color: #5cadff;
        box-shadow: 0 0 5px #2d8cf0;
        
        .top-msg-inner-ring {
            background-color: #2d8cf0;
        }
    }
    
    .top-msg-num {
        box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
    }
}

.top-msg-right {
    right: calc(3vw + 15px);

    .top-msg-icon {
        background-color: #ff985e;
        box-shadow: 0 0 5px #ff7a39;
        
        .top-msg-inner-ring {
            background-color: #ff7a39;
        }
    }
    
    .top-msg-num {
        box-shadow: 0 0 10px rgba(255, 122, 57, .2) inset;
        border-radius: 12px 0 0 12px;
        padding: 0 20px 0 12px;
    }
}

.top-msg-medal {
    right: calc(3vw + 15px);
    top: 40px;

    .top-msg-icon {
        background-color: #f3bb3a;
        box-shadow: 0 0 5px #e6a821;
        
        .top-msg-inner-ring {
            background-color: #e6a821;
        }
    }
    
    .top-msg-num {
        box-shadow: 0 0 10px rgba(230, 168, 33, .2) inset;
        border-radius: 12px 0 0 12px;
        padding: 0 20px 0 12px;
    }
}
</style>
