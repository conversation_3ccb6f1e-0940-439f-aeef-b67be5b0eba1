/**
 * 活动数据管理混入
 * 包含活动数据获取、用户状态管理、关卡数据处理等方法
 */
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'

export default {
    methods: {
        /**
         * 获取活动详情
         * @param {Boolean} justUpdate 是否只是更新数据
         * @returns {Boolean} 是否获取成功
         */
        async getDetail(justUpdate = false) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: { active_id: this.id },
            })

            if (!res?.data?.['active_details']) {
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }

            if (res.data.active_more_data?.['active_conf_set']?.rush_round_set) {
                this.rush_round_set = res.data.active_more_data['active_conf_set'].rush_round_set
            }

            // #ifndef H5
            this.$refs.expirationReminder.open(res.data.active_details)
            // #endif

            const detail = res.data['active_details']
            const app = getApp()
            app.globalData.activity_detail = JSON.parse(JSON.stringify(detail))

            let rank_set = {}
            if (detail.rank_set) {
                rank_set = detail.rank_set
                this.rank_set = detail.rank_set
            }

            if (!justUpdate && app.globalData['userid'] === detail.userid) {
                this.is_my_activity = true
            }
            
            this.detail = detail
            this.integral_unit = detail.conf.active.integral?.unit || '积分'
            my_storage.setActivityCloaeAdStorage(this.id, rank_set.closed_AD)

            if (detail.conf) {
                this.processActivityConfig(detail.conf, rank_set)
            }

            if (rank_set.shield_other) {
                this.$uni.hideHomeButton()
                // 更新纯净版缓存信息
                utils.updateShieldOtherInfo(this.detail)
            }

            this.addLookRecords()

            if (detail.name) this.$uni.setNavigationBarTitle(detail.name)

            return true
        },

        /**
         * 处理活动配置
         * @param {Object} conf 活动配置
         * @param {Object} rankSet 排行设置
         */
        processActivityConfig(conf, rankSet) {
            if (conf.active) {
                const active = conf.active
                if (!this.screen_pic && active.screen_pic) {
                    this.screenPicShow(active.screen_pic)
                }

                if (active.news?.news_id) this.changeDetailContent(active.news.news_id)

                const activeDetailsNoticeList = this.setActiveDetailsNotice(active, rankSet)
                this.active_details_notice_list = activeDetailsNoticeList
                
                this.setRightIcon(this.detail, activeDetailsNoticeList)
                
                if (active.start_challenge_button_set) {
                    this.start_challenge_button_set = active.start_challenge_button_set
                    delete active.start_challenge_button_set
                }
            }

            if (conf.must_submit) {
                const mustSubmit = conf.must_submit
                delete conf.must_submit
                if (mustSubmit.length) {
                    mustSubmit.forEach(v => v.value = v.value || '')
                    this.must_submit = mustSubmit
                }
            }
            
            if (conf.AI_motion) {
                // 把AI运动设置缓存到页面，不设置到页面data里面
                if (conf.AI_motion.motion_list?.length) this.AI_motion_list = conf.AI_motion.motion_list
                delete conf.AI_motion
            }
        },

        /**
         * 获取用户状态
         */
        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: { active_id: this.id }
            })

            if (res?.data?.user_details) {
                const attendDetails = res.data.user_details
                this.is_joining = true
                this.checked = attendDetails.checked || 0

                if (attendDetails.headimg) this.headimg = attendDetails.headimg

                if (attendDetails.must_submit) {
                    this.must_submit.forEach((v, i) => {
                        attendDetails.must_submit.forEach(vv => {
                            if (vv.title === v.title) this.$set(this.must_submit[i], 'value', vv.value)
                        })
                    })
                }
                
                const deleteKeys = ['active_id', 'active_types', 'userid', 'must_submit', 'headimg', 'shopid', 'create_time', 'update_time']
                deleteKeys.forEach(key => {
                    if (attendDetails.hasOwnProperty(key)) delete attendDetails[key]
                })

                this.user_details = attendDetails

                await this.getTodayIntegral()
                await this.getBmiValue()
            } else {
                this.no_attend = true
                if (this.screen_pic_show) return
                this.passwordDialogShow()
            }
        },

        /**
         * 获取今日积分
         */
        async getTodayIntegral() {
            if (!this.rank_set['rushing_round_index_current_day_integral']) return

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
                data: {
                    active_id: this.id,
                    date: utils.getDay(0, true),
                    total_integral: 1,
                    page: 1,
                    perpage: 1
                }
            })

            this.todayIntegral = res?.data?.total_integral || 0
        },

        /**
         * 获取BMI值
         */
        async getBmiValue() {
            const jobList = this.detail?.conf?.active?.job_set?.job_list || []

            // 只有添加了体脂任务才显示BMI值
            const open = jobList.some(item => item.types === 28)
            if (!open) return

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.lose_weight.user/user_weight_height_list',
                data: {
                    active_id: this.id,
                    myself: 1,
                    page: 1,
                    perpage: 1
                }
            })

            let { height = 0, weight = 0 } = res?.data?.list?.data?.[0] || {}
            if (!height || !weight) return

            height /= 1000
            weight /= 1000

            const heightInMeters = height / 100
            const bmi = Number((weight / (heightInMeters * heightInMeters)).toFixed(1))

            const bmiCategory = bmi => {
                if (bmi < 18.5) return '偏瘦'
                if (bmi >= 18.5 && bmi < 24) return '正常'
                if (bmi >= 24 && bmi < 28) return '偏胖'
                if (bmi >= 28) return '肥胖'
            }

            this.bmi_value = {
                value: bmi,
                category: bmiCategory(bmi)
            }
        },

        /**
         * 获取关卡列表
         */
        async getLevelList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: { active_id: this.id }
            })

            const list = res?.data?.['map_point_list'] || []
            const levelList = list.filter(item => item.types === 0)
            if (!levelList?.length) return
            this.level_list = levelList
            this.level_count = levelList.length
            this.levelNames = levelList.map(item => item.name)
        },

        /**
         * 设置当前关卡
         */
        async setCurrentLevel() {
            if (!this.level_list?.length) return
            let currentLevelIndex = this.user_details?.['rushed_round'] || 0
            if (currentLevelIndex > this.level_list.length - 1) {
                currentLevelIndex = this.level_list.length - 1
            }
            this.current_level = this.level_list[currentLevelIndex]
            this.current_level_index = currentLevelIndex
            this.current_level_image = this.current_level.map_pic
            this.levelImageFixed = !!this.current_level?.conf?.['image_fixed']

            const currentLevelId = this.current_level_id
            this.current_level_id = this.current_level.id
            if (currentLevelId !== null && currentLevelId !== this.current_level_id) {
                this.nextLevelPopupShow()
            }
        },

        /**
         * 添加查看记录
         */
        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },

        /**
         * 设置活动详情通知
         * @param {Object} active 活动配置
         * @param {Object} rankSet 排行设置
         * @returns {Array} 通知列表
         */
        setActiveDetailsNotice(active, rankSet) {
            if (!active.active_details_notice) return []

            const set = active.active_details_notice
            if (!set?.open || !rankSet.active_details_notice) return []

            let noticeList = set.notice_list
            if (!set.notice_list) {
                noticeList = [{
                    news_id: set.news_id,
                    news_title: set.news_title,
                    confirm_text: set.confirm_text
                }]
            }

            delete active.active_details_notice

            return noticeList
        },

        /**
         * 重新加载数据
         * @param {Boolean} init 是否为初始化
         */
        async reLoadData(init = false) {
            await this.getUserStatus()
            await this.getLevelList()
            await this.setCurrentLevel()
            if (!init) await this.certificateUnlockCheck()
        }
    }
}
