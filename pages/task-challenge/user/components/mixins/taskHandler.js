/**
 * 任务处理混入
 * 包含各种任务类型的跳转逻辑和处理方法
 */
import utils from '@/utils/utils.js'

export default {
    methods: {
        /**
         * 任务时间检查
         * @returns {Boolean} 是否在任务时间范围内
         */
        taskTimeCheck() {
            let { begin_time, end_time } = this.detail.conf.active.job_set || {}
            if (!begin_time && !end_time) return true

            begin_time ||= '00:00:00'
            end_time ||= '23:59:59'
            
            const now = new Date().getTime()
            const today = utils.getDay(0, true, '/')
            const begin = new Date(`${today} ${begin_time}`).getTime()
            const end = new Date(`${today} ${end_time}`).getTime()
            
            if (now < begin) {
                this.$uni.showToast(`${begin_time} 后才能开始任务`)
                return false
            }
            if (now > end) {
                this.$uni.showToast(`${end_time} 后不能做任务, 请明天 ${begin_time} 后再来`)
                return false
            }
            
            return true
        },

        /**
         * 活动时间检查
         * @returns {Boolean} 是否在活动时间范围内
         */
        activeTimeCheck() {
            const now_time = new Date().getTime()
            const { begin_time, end_time } = this.detail

            if (begin_time * 1000 > now_time) {
                const date = utils.unitTimeToDate(begin_time * 1000, true)
                this.$uni.showModal(`活动未开始。活动开始时间为${date}`)
                return false
            }
            if (end_time * 1000 < now_time) {
                const date = utils.unitTimeToDate(end_time * 1000, true)
                this.$uni.showModal(`活动已结束。活动结束时间为${date}`)
                return false
            }
            
            return true
        },

        /**
         * 参与检查
         * @param {Object} item 任务项（可选）
         * @returns {Boolean} 是否可以参与
         */
        joinCheck(item = null) {
            if (!this.is_joining) {
                // 体验版不报名直接进入的任务详情，报名弹窗点直接进入的时候需要知道是什么任务
                if (item) this.trial_no_join_just_go_task_item = item
                this.joinActivity()
                return false
            }
            if (!this.checked) {
                this.$uni.showToast('报名还没有审核通过哦')
                return false
            }
            
            return true
        },

        /**
         * 获取任务描述文本
         * @param {Object} task 任务对象
         * @returns {String} 任务描述
         */
        getTaskDescribe(task) {
            const unit = this.detail.conf.active.integral?.unit || '积分'

            const completeRewardTypes = [8, 10, 11, 13, 14, 19, 20, 21, 25, 30, 31, 32, 33, 34]
            if (completeRewardTypes.includes(task.types)) {
                return `完成任务奖励${task.integral}${unit}`
            }

            const describeOptions = {
                1: () => `当日步数达到${task.min}奖励${task.integral}${unit}`,
                2: () => {
                    const { exam_reward_num, exam_reward } = this.detail.conf.active.integral || {}
                    if (exam_reward_num && exam_reward) {
                        return `答题每获得${exam_reward}分奖励${exam_reward_num}${unit}`
                    }
                    return ''
                },
                3: () => `每发布1条动态奖励${task.integral}${unit}`,
                5: () => `阅读奖励${unit}`,
                6: () => {
                    const daily_sign = this.detail.conf.active.daily_sign
                    if (!daily_sign) return ''
                    const { circle_set, integral, types } = daily_sign
                    if (types === 1) return `每日签到奖励${integral}${unit}`
                    return `按照连续签到天数奖励${circle_set[0].integral}到${circle_set[circle_set.length - 1].integral}${unit}`
                },
                7: () => `完成拼图奖励${unit}`,
                9: () => {
                    if (task.hasOwnProperty('per_integral')) {
                        return `每答对1题奖励${task.per_integral || 0}${unit}`
                    }
                    return `完成任务奖励${task.integral || 0}${unit}`
                },
                12: () => {
                    if (task.hasOwnProperty('per_integral')) {
                        return `接到1个奖励${task.per_integral || 0}${unit}`
                    }
                    return `完成任务奖励${task.integral || 0}${unit}`
                },
                15: () => `接龙一次奖励${task.per_integral}${unit}`,
                16: () => `打中一只奖励${task.per_integral}${unit}`,
                18: () => {
                    if (task.hasOwnProperty('per_integral')) {
                        return `每答对1题奖励${task.per_integral || 0}${unit}`
                    }
                    return `完成任务奖励${task.integral || 0}${unit}`
                },
                22: () => `每连一个红包奖励${task.per_integral || 0}${unit}`,
                23: () => `每打中一个年兽奖励${task.per_integral || 0}${unit}`,
                24: () => `每击中一架飞机奖励${task.per_integral || 0}${unit}`,
                26: () => `每消除一组奖励${task.per_integral || 0}${unit}`,
                28: () => {
                    let info = `体脂每减少${task.num}%, 奖励${task.integral}${unit}`
                    if (task.max_integral) info += `, 奖励上限${task.max_integral}${unit}`
                    return info
                },
                29: () => {
                    let info = `腰围每减少${task.num}cm, 奖励${task.integral}${unit}`
                    if (task.max_integral) info += `, 奖励上限${task.max_integral}${unit}`
                    return info
                },
                36: () => `每数1张钱奖励${task.per_integral || 0}${unit}`,
                37: () => {
                    const accuracy = this.detail.conf.active?.tongue_twister?.accuracy || 0
                    if (accuracy) return `朗读准确率达到${accuracy}%, 奖励${task.integral || 0}${unit}`
                    return `完成任务奖励${task.integral || 0}${unit}`
                },
                38: () => `每进1球奖励${task.per_integral || 0}${unit}`,
                39: () => `每奔跑1米奖励${task.per_integral || 0}${unit}`,
                40: () => `每消除1个元素奖励${task.per_integral || 0}${unit}`,
                41: () => `每打爆1个气球奖励${task.per_integral || 0}${unit}`,
                42: () => `每跳过1个平台奖励${task.per_integral || 0}${unit}`,
                43: () => `每前进1米奖励${task.per_integral || 0}${unit}`,
                45: () => `每发布1条动态奖励${task.integral}${unit}`,
                46: () => {
                    if (this.detail.conf.active.bo_bing?.reward_type === 2) return ''
                    return `骰子每摇出1点奖励${task.per_integral}${unit}`
                },
                47: () => `每抓住1个月饼奖励${task.per_integral}${unit}`,
                48: () => `每完成1个俯卧撑奖励${task.per_integral}${unit}`,
            }

            return describeOptions[task.types]?.() || ''
        },

        /**
         * 任务成功检查
         * @param {String} pointId 关卡ID
         */
        successCheck(pointId) {
            this.taskListShow(pointId, true)
            setTimeout(() => {
                this.reLoadData()
            }, 1000)
        },

        /**
         * 体验版不报名直接进入任务
         */
        trialNoJoinJustGoTask() {
            // 鲜繁定制开发，神木的，需要单独一个页面显示任务列表
            if (this.id === 'e500dedf72811dfd04d474c3ceb1ee5c') return this.shenMuGoList()

            if (!this.trial_no_join_just_go_task_item) return this.$uni.showToast('没有指定任务')
            this.toCompleteTask(this.trial_no_join_just_go_task_item, true)
        },

        /**
         * 复制文本到剪贴板
         * @param {String} data 要复制的数据
         * @param {Boolean} hideToast 是否隐藏提示
         */
        copy(data, hideToast = false) {
            uni.setClipboardData({
                data,
                success: () => hideToast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        }
    }
}
