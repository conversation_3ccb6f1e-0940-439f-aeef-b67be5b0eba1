<template>
    <!-- 关卡背景图组件 -->
    <view class="current-level-image-container" v-if="currentLevelImage">
        <!-- 关卡背景图片 -->
        <image 
            v-if="levelImageFixed" 
            class="current-level-image" 
            style="height: 100vh"
            :src="currentLevelImage" 
            mode="aspectFill"
        />

        <image 
            v-else 
            class="current-level-image" 
            :src="currentLevelImage" 
            mode="widthFix"
            @load="handleImageLoad" 
            @click="handleImageClick"
        />

        <!-- DIY任务区域 -->
        <template v-if="!levelImageFixed && imageDiyTaskSet.length">
            <view 
                class="image-diy-task" 
                v-for="(item, index) in imageDiyTaskSet" 
                :key="index"
                :style="{
                    width: item.width, 
                    height: item.height, 
                    top: item.y, 
                    left: item.x
                }"
                @click="handleDiyTaskClick(item.types)"
            ></view>
        </template>
    </view>
</template>

<script>
export default {
    name: 'LevelBackground',
    props: {
        // 当前关卡背景图片
        currentLevelImage: {
            type: String,
            default: ''
        },
        // 关卡背景图是否固定不能滑动
        levelImageFixed: {
            type: Boolean,
            default: false
        },
        // DIY任务设置
        imageDiyTaskSet: {
            type: Array,
            default: () => []
        },
        // 当前关卡信息
        currentLevel: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            scale: 1 // 图片缩放比例
        }
    },
    methods: {
        /**
         * 处理图片加载完成
         * @param {Object} e 加载事件对象
         */
        handleImageLoad(e) {
            const { width } = e.detail
            let { windowWidth } = uni.getSystemInfoSync()
            const scale = width / windowWidth
            this.scale = scale

            // 延迟执行滚动到指定位置
            setTimeout(() => {
                this.$nextTick(() => {
                    const top = this.currentLevel?.margin_top
                    if (!top) return false
                    const scrollTop = top / scale

                    uni.pageScrollTo({
                        scrollTop,
                        duration: 800
                    })
                })
            }, 300)

            // 触发图片加载完成事件
            this.$emit('imageLoad', { scale })
        },

        /**
         * 处理图片点击
         * @param {Object} e 点击事件对象
         */
        handleImageClick(e) {
            // 如果有DIY任务设置，不处理图片点击
            if (this.imageDiyTaskSet?.length) return

            // 触发图片点击事件，传递点击坐标和缩放比例
            this.$emit('imageClick', {
                detail: e.detail,
                scale: this.scale
            })
        },

        /**
         * 处理DIY任务区域点击
         * @param {Number} types 任务类型
         */
        handleDiyTaskClick(types) {
            this.$emit('diyTaskClick', types)
        }
    }
}
</script>

<style lang="scss" scoped>
.current-level-image-container {
    position: relative;

    .current-level-image {
        width: 100vw;
        height: auto;
        display: block;
    }

    .image-diy-task {
        position: absolute;
        /* 可以添加一些调试样式来查看DIY任务区域 */
        /* border: 1px solid rgba(255, 0, 0, 0.3); */
        /* background-color: rgba(255, 0, 0, 0.1); */
    }
}
</style>
