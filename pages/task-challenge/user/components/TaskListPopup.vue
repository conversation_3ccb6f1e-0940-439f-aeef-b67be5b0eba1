<template>
    <!-- 任务列表弹窗组件 -->
    <uni-popup ref="taskListPopup" type="bottom" :safe-area="false">
        <!-- 顶部关闭按钮 -->
        <view class="flex-kai">
            <view></view>
            <view class="p10">
                <uni-icons type="close" color="#ffffff" size="28" @click="close"/>
            </view>
        </view>
        
        <!-- 任务列表内容 -->
        <view class="task-list-popup" :style="{backgroundColor: taskPopupStyle.border_color}">
            <view class="popup-container" :style="{backgroundColor: taskPopupStyle.bg_color}">
                <!-- 关卡标题 -->
                <view class="task-list-popup-title" @click="handleTitleClick">
                    <template v-if="justOneLevelNotLevel">
                        <template v-if="isNaN(activeDays)">{{ activeDays }}</template>
                        <template v-else>
                            <text>第</text>
                            <text class="font24 plr5">{{ activeDays }}</text>
                            <text>天</text>
                        </template>
                    </template>
                    <template v-else>{{ currentLevelName }}</template>
                </view>

                <!-- 加载状态 -->
                <view v-if="taskListLoading" class="text-center" style="padding: 100px 0;">
                    <load-ani/>
                    <view class="font12" :style="{color: taskPopupStyle.text_color}">任务加载中</view>
                </view>

                <!-- 关卡任务描述 -->
                <view 
                    v-if="levelTaskDescription" 
                    class="task-tips font12 text-center pt10"
                    :style="{color: taskPopupStyle.text_color}"
                >
                    <text>{{ levelTaskDescription }}</text>
                    <text v-if="isJoining && clearanceType === 2" class="pl5">
                        (已获得{{ userIntegralAll }}{{ integralUnit }})
                    </text>
                </view>

                <!-- 任务列表 -->
                <view class="task-list">
                    <!-- 普通任务 -->
                    <view v-for="(item, index) in taskList" :key="index">
                        <task-list-item 
                            v-if="!item.hidden" 
                            :item="item"
                            :style="taskPopupStyle.task_item"
                            :active-id="activeId"
                            :sport-moment-name="sportMomentName"
                            :hide-sign-list-enter="!!hideSignListEnter"
                            @toCompleteTask="handleCompleteTask(item)"
                            @lookWeekTaskNews="handleLookWeekTaskNews"
                            @toSportMomentList="handleToSportMomentList"
                        />
                    </view>

                    <!-- 商店任务 -->
                    <task-list-item 
                        v-if="taskListShowShopSet" 
                        :show-sport-moment="false"
                        :item="taskListShowShopSet" 
                        :style="taskPopupStyle.task_item"
                        @toCompleteTask="handleToShop"
                    />

                    <!-- 抽奖任务 -->
                    <task-list-item 
                        v-if="taskListShowLotterySet" 
                        :show-sport-moment="false"
                        :item="taskListShowLotterySet" 
                        :style="taskPopupStyle.task_item"
                        @toCompleteTask="handleToLottery"
                    />

                    <!-- 签到任务 -->
                    <task-list-item 
                        v-if="taskListShowSignSet" 
                        :show-sport-moment="false"
                        :item="taskListShowSignSet" 
                        :style="taskPopupStyle.task_item"
                        @clickTitle="handleToSignSquare" 
                        @toCompleteTask="handleToSign"
                    />
                </view>
            </view>
        </view>
    </uni-popup>
</template>

<script>
import taskListItem from '../../components/task-list-item.vue'

export default {
    name: 'TaskListPopup',
    components: {
        taskListItem
    },
    props: {
        // 活动ID
        activeId: {
            type: String,
            required: true
        },
        // 任务列表
        taskList: {
            type: Array,
            default: () => []
        },
        // 任务列表加载状态
        taskListLoading: {
            type: Boolean,
            default: false
        },
        // 关卡任务描述
        levelTaskDescription: {
            type: String,
            default: ''
        },
        // 是否已参与活动
        isJoining: {
            type: Boolean,
            default: false
        },
        // 过关类型
        clearanceType: {
            type: Number,
            default: 1
        },
        // 用户总积分
        userIntegralAll: {
            type: Number,
            default: 0
        },
        // 积分单位
        integralUnit: {
            type: String,
            default: '积分'
        },
        // 是否只有一关且不显示关卡
        justOneLevelNotLevel: {
            type: Boolean,
            default: false
        },
        // 活动天数
        activeDays: {
            type: [String, Number],
            default: 0
        },
        // 当前关卡名称
        currentLevelName: {
            type: String,
            default: ''
        },
        // 任务弹窗样式
        taskPopupStyle: {
            type: Object,
            default: () => ({})
        },
        // 运动圈名称
        sportMomentName: {
            type: String,
            default: '运动圈'
        },
        // 是否隐藏签到列表入口
        hideSignListEnter: {
            type: Boolean,
            default: false
        },
        // 商店任务设置
        taskListShowShopSet: {
            type: Object,
            default: null
        },
        // 抽奖任务设置
        taskListShowLotterySet: {
            type: Object,
            default: null
        },
        // 签到任务设置
        taskListShowSignSet: {
            type: Object,
            default: null
        }
    },
    methods: {
        /**
         * 打开任务列表弹窗
         */
        open() {
            this.$refs.taskListPopup.open()
        },

        /**
         * 关闭任务列表弹窗
         */
        close() {
            this.$refs.taskListPopup.close()
            this.$emit('close')
        },

        /**
         * 处理标题点击（复制活动ID）
         */
        handleTitleClick() {
            this.$emit('titleClick', this.activeId)
        },

        /**
         * 处理完成任务
         * @param {Object} item 任务项
         */
        handleCompleteTask(item) {
            this.$emit('completeTask', item)
        },

        /**
         * 处理查看周任务新闻
         * @param {Number} types 任务类型
         */
        handleLookWeekTaskNews(types) {
            this.$emit('lookWeekTaskNews', types)
        },

        /**
         * 处理前往运动圈列表
         * @param {Number} types 任务类型
         */
        handleToSportMomentList(types) {
            this.$emit('toSportMomentList', types)
        },

        /**
         * 处理前往商店
         */
        handleToShop() {
            this.$emit('toShop')
        },

        /**
         * 处理前往抽奖
         */
        handleToLottery() {
            this.$emit('toLottery')
        },

        /**
         * 处理前往签到
         */
        handleToSign() {
            this.$emit('toSign')
        },

        /**
         * 处理前往签到广场
         */
        handleToSignSquare() {
            this.$emit('toSignSquare')
        }
    }
}
</script>

<style lang="scss" scoped>
.task-list-popup {
    position: relative;
    background-color: #FFD67A;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;

    .popup-container {
        background-color: #fff;
        border-radius: 10px;
        padding: 10px;
    }

    .task-list-popup-title {
        position: absolute;
        top: -22px;
        left: 50%;
        margin-left: -110px;
        width: 220px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url("https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .task-tips {
        margin-top: -5px;
    }

    .task-list {
        overflow-y: auto;
        margin: 10px 0;
        max-height: calc(90vh - 150px);
    }
}
</style>
