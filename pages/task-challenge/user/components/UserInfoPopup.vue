<template>
    <!-- 用户信息弹窗组件 -->
    <uni-popup ref="userInfoPopup" type="center" @touchmove.stop.prevent="">
        <view v-if="activityDetail && activityDetail.conf && activityDetail.conf.active" class="uni-popup-info">
            <view class="uni-popup-info-title">活动报名信息</view>
            <view class="popup-container">
                <!-- 用户头像部分 -->
                <view class="text-center p10">
                    <image
                        class="headimg"
                        :src="userHeadimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                        mode="aspectFill"
                    />
                    <view>
                        <text class="color-primary" @click="handleUpdateHeadimg">更改头像</text>
                    </view>
                </view>

                <!-- 必填信息列表 -->
                <view
                    class="user-info-item color-content"
                    v-for="(item, index) in mustSubmitList"
                    :key="index"
                    @click="handleUpdateAttendDetail"
                >
                    <text>{{item.title}}:</text>
                    <text class="plr5">
                        <template v-if="item.value">{{ item.value }}</template>
                        <template v-else>
                            <template v-if="item.types === 1">未填写</template>
                            <template v-if="item.types === 2">未选择</template>
                        </template>
                    </text>
                    <text v-if="isJoining" class="iconfont icon-edit color-sub"></text>
                </view>

                <!-- 性别信息 -->
                <view 
                    v-if="activityDetail.conf.active.sex_required !== -1" 
                    class="user-info-item color-content"
                    @click="handleUpdateAttendDetail"
                >
                    <text>性别:</text>
                    <text class="plr5">{{ sexTitle || '保密' }}</text>
                    <text v-if="isJoining" class="iconfont icon-edit color-sub"></text>
                </view>

                <!-- 队伍信息 -->
                <view 
                    v-if="showTeamInfo" 
                    class="user-info-item color-content"
                    @click="handleUpdateAttendDetail"
                >
                    <text>队伍:</text>
                    <text class="plr5">
                        {{ getTeamDisplayName() }}
                    </text>
                    <text v-if="isJoining && !userDetails.team_id" class="iconfont icon-edit color-sub"></text>
                </view>

                <!-- 当前关卡信息 -->
                <view v-if="!justOneLevelNotLevel" class="user-info-item color-content">
                    <text>当前关卡:</text>
                    <text class="plr5">{{ currentLevelName }}</text>
                </view>

                <!-- 累计积分信息 -->
                <view class="user-info-item color-content" @click="handleLookIntegralRecord">
                    <text>累计{{ integralUnit }}:</text>
                    <text class="pl5">{{ userDetails.integral_all || 0 }}</text>
                    <text class="iconfont icon-more color-content"></text>
                </view>

                <!-- 剩余积分信息 -->
                <view 
                    v-if="showLeftIntegral" 
                    class="user-info-item color-content" 
                    @click="handleToShop"
                >
                    <text>剩余{{ integralUnit }}:</text>
                    <text class="pl5">{{ userDetails.integral_left || 0 }}</text>
                    <text class="iconfont icon-more color-content"></text>
                </view>

                <!-- BMI信息 -->
                <template v-if="bmiValue">
                    <view class="user-info-item color-content" @click="handleShowBmiAnalysis">
                        <text>BMI:</text>
                        <text class="plr5">{{ bmiValue.value }}</text>
                        <text class="font14">({{ bmiValue.category }})</text>
                        <text class="iconfont color-sub icon-question-mark-up font18"></text>
                    </view>
                </template>

                <!-- 勋章信息 -->
                <view 
                    v-if="showMedalInfo" 
                    class="user-info-item color-content"
                    @click="handleLookMedal"
                >
                    <text>我的勋章:</text>
                    <text class="pl5">{{ userDetails.medal_num || 0 }}枚</text>
                    <text class="iconfont icon-more color-content"></text>
                </view>

                <!-- 活动报告（测试环境） -->
                <view 
                    v-if="showActivityReport" 
                    class="user-info-item color-content" 
                    @click="handleToActiveReport"
                >
                    <text>活动报告:</text>
                    <text class="pl5">查看报告</text>
                    <text class="iconfont icon-more color-content"></text>
                </view>

                <!-- 广告位 -->
                <template v-if="showAd">
                    <view class="flex-center">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </view>

        <!-- 关闭按钮 -->
        <view class="flex-all-center" @click="close">
            <uni-icons type="close" size="28" color="#ffffff"/>
        </view>
    </uni-popup>
</template>

<script>
export default {
    name: 'UserInfoPopup',
    props: {
        // 活动详情
        activityDetail: {
            type: Object,
            default: () => ({})
        },
        // 用户详情
        userDetails: {
            type: Object,
            default: () => ({})
        },
        // 用户头像
        userHeadimg: {
            type: String,
            default: ''
        },
        // 必填信息列表
        mustSubmitList: {
            type: Array,
            default: () => []
        },
        // 是否已参与活动
        isJoining: {
            type: Boolean,
            default: false
        },
        // 积分单位
        integralUnit: {
            type: String,
            default: '积分'
        },
        // 当前关卡名称
        currentLevelName: {
            type: String,
            default: ''
        },
        // 是否只有一关且不显示关卡
        justOneLevelNotLevel: {
            type: Boolean,
            default: false
        },
        // 排行设置
        rankSet: {
            type: Object,
            default: () => ({})
        },
        // BMI值
        bmiValue: {
            type: Object,
            default: null
        },
        // 性别标签
        sexLabel: {
            type: Array,
            default: () => []
        },
        // 是否显示广告
        showAd: {
            type: Boolean,
            default: true
        },
        // 环境版本和用户ID（用于显示活动报告）
        envVersion: {
            type: String,
            default: ''
        },
        who: {
            type: Number,
            default: 0
        }
    },
    computed: {
        /**
         * 是否显示队伍信息
         */
        showTeamInfo() {
            return this.rankSet.team_group_open
        },

        /**
         * 是否显示剩余积分
         */
        showLeftIntegral() {
            return this.rankSet.gift_goods && !this.activityDetail.conf.active.integral_left_hide
        },

        /**
         * 是否显示勋章信息
         */
        showMedalInfo() {
            return this.rankSet.medalSetting
        },

        /**
         * 是否显示活动报告
         */
        showActivityReport() {
            return this.who === 288 && this.envVersion !== 'release'
        },

        /**
         * 性别显示文本
         */
        sexTitle() {
            const sex = this.userDetails?.sex
            if (!sex) return ''
            return this.sexLabel.find(item => item.value === sex)?.title || ''
        }
    },
    methods: {
        /**
         * 打开用户信息弹窗
         */
        open() {
            this.$refs.userInfoPopup.open()
        },

        /**
         * 关闭用户信息弹窗
         */
        close() {
            this.$refs.userInfoPopup.close()
            this.$emit('close')
        },

        /**
         * 获取队伍显示名称
         */
        getTeamDisplayName() {
            const { team_details, team_id } = this.userDetails
            return (team_details && team_details.name) || team_id || '未加入队伍'
        },

        /**
         * 处理更改头像
         */
        handleUpdateHeadimg() {
            this.$emit('updateHeadimg')
        },

        /**
         * 处理更新报名详情
         */
        handleUpdateAttendDetail() {
            this.$emit('updateAttendDetail')
        },

        /**
         * 处理查看积分记录
         */
        handleLookIntegralRecord() {
            this.$emit('lookIntegralRecord')
        },

        /**
         * 处理前往商店
         */
        handleToShop() {
            this.$emit('toShop')
        },

        /**
         * 处理显示BMI分析
         */
        handleShowBmiAnalysis() {
            this.$emit('showBmiAnalysis')
        },

        /**
         * 处理查看勋章
         */
        handleLookMedal() {
            this.$emit('lookMedal')
        },

        /**
         * 处理前往活动报告
         */
        handleToActiveReport() {
            this.$emit('toActiveReport')
        }
    }
}
</script>

<style lang="scss" scoped>
.uni-popup-info {
    width: 90vw;
    position: relative;
    background-color: #FFD67A;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;

    .popup-container {
        background-color: #fff;
        border-radius: 10px;
        padding: 10px;
    }

    .uni-popup-info-title {
        position: absolute;
        top: -22px;
        left: 50%;
        margin-left: -110px;
        width: 220px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url("https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .headimg {
        width: 80px;
        height: 80px;
        border-radius: 50%;
    }

    .user-info-item {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #feedcd;
        background-color: #fffdfa;
        border-radius: 5px;
    }
}
</style>
