<template>
    <!-- 活动详情弹窗组件 -->
    <uni-popup ref="activityDetailPopup" type="center" @touchmove.stop.prevent="">
        <view v-if="activityDetail && activityDetail.conf && activityDetail.conf.active" class="uni-popup-info detail-popup">
            <view class="uni-popup-info-title">活动说明</view>

            <view class="popup-container">
                <scroll-view scroll-y="true" class="detail-popup-detail">
                    <!-- 活动规则部分 -->
                    <view v-if="!activityDetail.conf.active.activity_rules_hide" style="padding-bottom: 30px;">
                        <view class="color-content font16">
                            活动参与方式：
                            {{ getEnterTypesText(activityDetail.conf.active.enter_types) }}
                        </view>
                    </view>

                    <!-- 活动说明内容 -->
                    <view v-if="activityDetail.content || newsDetail">
                        <view class="color-content font16">
                            <template v-if="activityDetail.content && !newsDetail">
                                <rich-text :nodes="activityDetail.content" space="nbsp"></rich-text>
                            </template>
                            <template v-if="newsDetail">
                                <template v-if="newsDetail.content">
                                    <u-parse :content="newsDetail.content"/>
                                </template>
                            </template>
                        </view>
                    </view>
                </scroll-view>

                <!-- 广告位 -->
                <view v-if="!loading && showAd" class="flex-center">
                    <xwy-ad :ad_type="66"></xwy-ad>
                </view>
            </view>
        </view>
        
        <!-- 关闭按钮 -->
        <view class="flex-all-center" @click="close">
            <uni-icons type="close" size="28" color="#ffffff"/>
        </view>
    </uni-popup>
</template>

<script>
export default {
    name: 'ActivityDetailPopup',
    props: {
        // 活动详情数据
        activityDetail: {
            type: Object,
            default: () => ({})
        },
        // 新闻详情数据
        newsDetail: {
            type: Object,
            default: null
        },
        // 是否显示加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 是否显示广告
        showAd: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            // 活动参与方式文本映射
            enterTypesMap: {
                1: '自由报名参与活动',
                2: '需要输入密码才能报名',
                3: '报名需要审核通过才能参与活动',
                4: '使用管理员导入的名单报名'
            }
        }
    },
    methods: {
        /**
         * 打开活动详情弹窗
         */
        open() {
            this.$refs.activityDetailPopup.open()
        },

        /**
         * 关闭活动详情弹窗
         */
        close() {
            this.$refs.activityDetailPopup.close()
            this.$emit('close')
        },

        /**
         * 获取活动参与方式文本
         * @param {Number} enterTypes 参与方式类型
         * @returns {String} 参与方式文本
         */
        getEnterTypesText(enterTypes) {
            return this.enterTypesMap[enterTypes] || ''
        }
    }
}
</script>

<style lang="scss" scoped>
.uni-popup-info {
    position: relative;
    background-color: #FFD67A;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;

    .popup-container {
        background-color: #fff;
        border-radius: 10px;
        padding: 10px;
    }

    .uni-popup-info-title {
        position: absolute;
        top: -22px;
        left: 50%;
        margin-left: -110px;
        width: 220px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url("https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

.detail-popup {
    width: 95vw;
    box-sizing: border-box;

    .detail-popup-detail {
        max-height: calc(100vh - 300px);
        box-sizing: border-box;
        margin-top: 10px;
    }
}
</style>
